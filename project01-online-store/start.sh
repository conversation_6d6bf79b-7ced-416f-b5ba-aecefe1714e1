#!/bin/bash

# 在线商品展示平台 - 快速启动脚本

echo "🚀 启动在线商品展示平台..."

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ 错误: 请先安装 Node.js"
    exit 1
fi

# 检查npm是否安装
if ! command -v npm &> /dev/null; then
    echo "❌ 错误: 请先安装 npm"
    exit 1
fi

echo "✅ Node.js 和 npm 已安装"

# 启动后端服务
echo "🔧 启动后端服务..."
cd backend

# 检查是否已安装依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装后端依赖..."
    npm install
fi

# 启动后端服务（后台运行）
echo "🚀 启动后端服务器 (端口 3001)..."
npm run dev &
BACKEND_PID=$!

# 等待后端启动
sleep 3

# 启动前端服务
echo "🎨 启动前端服务..."
cd ../frontend

# 检查是否已安装依赖
if [ ! -d "node_modules" ]; then
    echo "📦 安装前端依赖..."
    npm install
fi

# 启动前端服务
echo "🚀 启动前端服务器 (端口 5173)..."
npm run dev &
FRONTEND_PID=$!

# 等待前端启动
sleep 5

echo ""
echo "🎉 项目启动成功！"
echo ""
echo "📱 前端地址: http://localhost:5173"
echo "🔧 后端地址: http://localhost:3001"
echo "🏥 健康检查: http://localhost:3001/health"
echo ""
echo "📚 API文档:"
echo "  - 商品列表: GET /api/products"
echo "  - 商品详情: GET /api/products/:id"
echo "  - 商品搜索: GET /api/products/search?q=关键词"
echo "  - 分类列表: GET /api/categories"
echo ""
echo "🛑 停止服务: Ctrl+C"
echo ""

# 等待用户中断
wait $FRONTEND_PID $BACKEND_PID
