# 在线商品展示平台 - 功能演示

## 项目演示指南

### 1. 项目启动

#### 启动后端服务
```bash
cd project01-online-store/backend
npm run dev
```
后端服务将运行在: http://localhost:3001

#### 启动前端服务
```bash
cd project01-online-store/frontend
npm run dev
```
前端服务将运行在: http://localhost:5173

### 2. 功能演示

#### 2.1 首页展示
- 访问: http://localhost:5173
- 功能点:
  - 英雄区域展示
  - 商品分类导航
  - 特色商品展示
  - 响应式设计

#### 2.2 商品列表页
- 访问: http://localhost:5173/products
- 功能点:
  - 商品网格展示
  - 筛选功能（价格、品牌、排序）
  - 分页功能
  - 商品卡片信息

#### 2.3 商品详情页
- 访问: http://localhost:5173/products/[商品ID]
- 功能点:
  - 商品图片展示
  - 详细信息展示
  - 规格参数
  - 相关商品推荐

#### 2.4 搜索功能
- 在顶部搜索框输入关键词
- 或访问: http://localhost:5173/search?q=iPhone
- 功能点:
  - 关键词搜索
  - 搜索结果展示
  - 搜索结果排序
  - 搜索结果分页

### 3. API接口测试

#### 3.1 获取商品列表
```bash
curl "http://localhost:3001/api/products"
```

#### 3.2 搜索商品
```bash
curl "http://localhost:3001/api/products/search?q=iPhone"
```

#### 3.3 获取商品详情
```bash
curl "http://localhost:3001/api/products/[商品ID]"
```

#### 3.4 健康检查
```bash
curl "http://localhost:3001/health"
```

### 4. 爬虫练习建议

#### 4.1 基础爬取练习
1. **爬取首页商品**
   - 目标: http://localhost:5173
   - 提取: 特色商品信息
   - 技术点: HTML解析、CSS选择器

2. **爬取商品列表**
   - 目标: http://localhost:5173/products
   - 提取: 商品基本信息
   - 技术点: 分页处理、数据提取

3. **爬取商品详情**
   - 目标: http://localhost:5173/products/[ID]
   - 提取: 完整商品信息
   - 技术点: 深度爬取、数据结构化

#### 4.2 进阶爬取练习
1. **搜索功能爬取**
   - 目标: 搜索结果页面
   - 技术点: 表单提交、动态内容

2. **分类浏览爬取**
   - 目标: 分类商品页面
   - 技术点: 分类遍历、数据去重

3. **API接口爬取**
   - 目标: REST API接口
   - 技术点: JSON数据处理、API认证

#### 4.3 高级爬取练习
1. **并发爬取**
   - 多线程/异步爬取商品信息
   - 技术点: 并发控制、速率限制

2. **数据存储**
   - 将爬取的数据存储到数据库
   - 技术点: 数据清洗、数据建模

3. **增量更新**
   - 定期更新商品信息
   - 技术点: 数据对比、增量处理

### 5. 数据结构示例

#### 5.1 商品数据结构
```json
{
  "id": "uuid",
  "name": "商品名称",
  "subtitle": "商品副标题",
  "description": "商品描述",
  "price": 999.00,
  "original_price": 1299.00,
  "stock": 100,
  "sales": 1200,
  "rating": 4.8,
  "review_count": 856,
  "brand": "品牌名称",
  "category_id": "分类ID",
  "images": ["图片URL1", "图片URL2"],
  "specifications": {
    "屏幕尺寸": "6.1英寸",
    "存储容量": "128GB"
  },
  "status": "active",
  "created_at": "2024-01-01T00:00:00Z",
  "updated_at": "2024-01-01T00:00:00Z"
}
```

#### 5.2 分类数据结构
```json
{
  "id": "uuid",
  "name": "分类名称",
  "parent_id": "父分类ID",
  "icon": "图标URL",
  "description": "分类描述",
  "sort_order": 1,
  "created_at": "2024-01-01T00:00:00Z"
}
```

### 6. 爬虫技术要点

#### 6.1 HTML解析
- 使用CSS选择器定位元素
- 提取文本和属性信息
- 处理嵌套结构

#### 6.2 数据提取
- 商品名称: `.product-name`
- 商品价格: `.current-price`
- 商品图片: `.product-image img`
- 商品链接: `.product-link`

#### 6.3 分页处理
- 分页参数: `?page=1&limit=20`
- 总页数获取: API响应中的pagination信息
- 循环遍历所有页面

#### 6.4 搜索处理
- 搜索URL: `/search?q=关键词`
- 搜索参数: `q`, `page`, `sort`, `order`
- 搜索结果解析

### 7. 常见问题和解决方案

#### 7.1 跨域问题
- 后端已配置CORS
- 前端使用代理配置

#### 7.2 数据加载
- 部分数据通过Ajax异步加载
- 需要等待数据加载完成

#### 7.3 图片处理
- 图片使用外部URL
- 需要处理图片加载失败的情况

### 8. 性能优化建议

#### 8.1 爬取频率控制
- 建议间隔1-2秒进行请求
- 避免对服务器造成过大压力

#### 8.2 数据缓存
- 缓存已爬取的数据
- 避免重复爬取相同内容

#### 8.3 错误处理
- 处理网络超时
- 处理HTTP错误状态码
- 实现重试机制

### 9. 扩展练习

#### 9.1 数据分析
- 分析商品价格分布
- 统计品牌商品数量
- 分析用户评价趋势

#### 9.2 数据可视化
- 制作商品价格图表
- 展示分类商品分布
- 创建销量排行榜

#### 9.3 监控系统
- 监控商品价格变化
- 跟踪新品上架
- 分析库存变化

这个演示指南为爬虫学习提供了完整的实战场景，从基础的HTML解析到高级的数据分析，涵盖了爬虫开发的各个方面。
