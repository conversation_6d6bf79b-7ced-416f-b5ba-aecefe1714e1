const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.SUPABASE_URL || 'https://pyebzvcjcfyoexphdhiq.supabase.co';
const supabaseKey = process.env.SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InB5ZWJ6dmNqY2Z5b2V4cGhkaGlxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk0NjA3ODUsImV4cCI6MjA2NTAzNjc4NX0.xRfa9fipUPfU5-4A3VTmpHZbmw4UC_0IHfg9Hp1K1Zg';

const supabase = createClient(supabaseUrl, supabaseKey);

module.exports = supabase;
