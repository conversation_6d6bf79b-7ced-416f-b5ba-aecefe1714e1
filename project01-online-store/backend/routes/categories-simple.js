const express = require('express');
const router = express.Router();
const supabase = require('../config/supabase');

// 获取所有分类
router.get('/', async (req, res) => {
  try {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .order('sort_order', { ascending: true });

    if (error) {
      console.error('获取分类列表错误:', error);
      return res.status(500).json({ error: '获取分类列表失败' });
    }

    // 构建简单的分类树结构
    const rootCategories = data.filter(cat => !cat.parent_id);
    const childCategories = data.filter(cat => cat.parent_id);

    const categoryTree = rootCategories.map(parent => ({
      ...parent,
      children: childCategories.filter(child => child.parent_id === parent.id)
    }));

    res.json(categoryTree);
  } catch (error) {
    console.error('获取分类列表错误:', error);
    res.status(500).json({ error: '服务器内部错误' });
  }
});

// 获取分类详情
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('获取分类详情错误:', error);
      return res.status(404).json({ error: '分类不存在' });
    }

    res.json(data);
  } catch (error) {
    console.error('获取分类详情错误:', error);
    res.status(500).json({ error: '服务器内部错误' });
  }
});

module.exports = router;
