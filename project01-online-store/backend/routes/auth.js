const express = require('express');
const router = express.Router();
const supabase = require('../config/supabase');

// 用户注册
router.post('/register', async (req, res) => {
  try {
    const { email, password, username } = req.body;

    // 验证输入
    if (!email || !password || !username) {
      return res.status(400).json({ error: '邮箱、密码和用户名都是必填项' });
    }

    if (password.length < 8) {
      return res.status(400).json({ error: '密码长度至少8位' });
    }

    if (username.length < 3 || username.length > 20) {
      return res.status(400).json({ error: '用户名长度应在3-20字符之间' });
    }

    // 检查用户名是否已存在
    const { data: existingUser } = await supabase
      .from('profiles')
      .select('username')
      .eq('username', username)
      .single();

    if (existingUser) {
      return res.status(400).json({ error: '用户名已存在' });
    }

    // 注册用户
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          username: username
        }
      }
    });

    if (error) {
      console.error('用户注册错误:', error);
      return res.status(400).json({ error: error.message });
    }

    // 创建用户资料
    if (data.user) {
      const { error: profileError } = await supabase
        .from('profiles')
        .insert([
          {
            id: data.user.id,
            username: username,
            email: email
          }
        ]);

      if (profileError) {
        console.error('创建用户资料错误:', profileError);
      }
    }

    res.json({
      message: '注册成功',
      user: {
        id: data.user?.id,
        email: data.user?.email,
        username: username
      }
    });
  } catch (error) {
    console.error('注册错误:', error);
    res.status(500).json({ error: '服务器内部错误' });
  }
});

// 用户登录
router.post('/login', async (req, res) => {
  try {
    const { email, password } = req.body;

    // 验证输入
    if (!email || !password) {
      return res.status(400).json({ error: '邮箱和密码都是必填项' });
    }

    // 登录
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password
    });

    if (error) {
      console.error('用户登录错误:', error);
      return res.status(401).json({ error: '邮箱或密码错误' });
    }

    // 获取用户资料
    const { data: profile } = await supabase
      .from('profiles')
      .select('username, avatar')
      .eq('id', data.user.id)
      .single();

    res.json({
      message: '登录成功',
      user: {
        id: data.user.id,
        email: data.user.email,
        username: profile?.username,
        avatar: profile?.avatar
      },
      session: {
        access_token: data.session.access_token,
        refresh_token: data.session.refresh_token
      }
    });
  } catch (error) {
    console.error('登录错误:', error);
    res.status(500).json({ error: '服务器内部错误' });
  }
});

// 用户登出
router.post('/logout', async (req, res) => {
  try {
    const { error } = await supabase.auth.signOut();

    if (error) {
      console.error('登出错误:', error);
      return res.status(500).json({ error: '登出失败' });
    }

    res.json({ message: '登出成功' });
  } catch (error) {
    console.error('登出错误:', error);
    res.status(500).json({ error: '服务器内部错误' });
  }
});

// 获取当前用户信息
router.get('/profile', async (req, res) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      return res.status(401).json({ error: '未提供认证令牌' });
    }

    const token = authHeader.replace('Bearer ', '');
    
    // 验证令牌
    const { data: { user }, error } = await supabase.auth.getUser(token);

    if (error || !user) {
      return res.status(401).json({ error: '无效的认证令牌' });
    }

    // 获取用户资料
    const { data: profile } = await supabase
      .from('profiles')
      .select('username, avatar')
      .eq('id', user.id)
      .single();

    res.json({
      id: user.id,
      email: user.email,
      username: profile?.username,
      avatar: profile?.avatar
    });
  } catch (error) {
    console.error('获取用户信息错误:', error);
    res.status(500).json({ error: '服务器内部错误' });
  }
});

module.exports = router;
