const express = require('express');
const router = express.Router();
const supabase = require('../config/supabase');

// 获取商品列表
router.get('/', async (req, res) => {
  try {
    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        categories:category_id (
          id,
          name
        )
      `)
      .eq('status', 'active')
      .order('created_at', { ascending: false })
      .limit(20);

    if (error) {
      console.error('获取商品列表错误:', error);
      return res.status(500).json({ error: '获取商品列表失败' });
    }

    res.json({
      data,
      pagination: {
        page: 1,
        limit: 20,
        total: data.length
      }
    });
  } catch (error) {
    console.error('获取商品列表错误:', error);
    res.status(500).json({ error: '服务器内部错误' });
  }
});

// 搜索商品
router.get('/search', async (req, res) => {
  try {
    const { q: query } = req.query;

    if (!query) {
      return res.status(400).json({ error: '搜索关键词不能为空' });
    }

    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        categories:category_id (
          id,
          name
        )
      `)
      .eq('status', 'active')
      .or(`name.ilike.%${query}%,description.ilike.%${query}%,brand.ilike.%${query}%`)
      .order('created_at', { ascending: false })
      .limit(20);

    if (error) {
      console.error('搜索商品错误:', error);
      return res.status(500).json({ error: '搜索失败' });
    }

    res.json({
      data,
      query,
      pagination: {
        page: 1,
        limit: 20,
        total: data.length
      }
    });
  } catch (error) {
    console.error('搜索商品错误:', error);
    res.status(500).json({ error: '服务器内部错误' });
  }
});

// 获取商品详情
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        categories:category_id (
          id,
          name,
          parent_id
        )
      `)
      .eq('id', id)
      .eq('status', 'active')
      .single();

    if (error) {
      console.error('获取商品详情错误:', error);
      return res.status(404).json({ error: '商品不存在' });
    }

    res.json(data);
  } catch (error) {
    console.error('获取商品详情错误:', error);
    res.status(500).json({ error: '服务器内部错误' });
  }
});

module.exports = router;
