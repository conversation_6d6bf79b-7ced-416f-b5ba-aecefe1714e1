const express = require('express');
const router = express.Router();
const supabase = require('../config/supabase');

// 获取所有分类（树形结构）
router.get('/', async (req, res) => {
  try {
    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .order('sort_order', { ascending: true });

    if (error) {
      console.error('获取分类列表错误:', error);
      return res.status(500).json({ error: '获取分类列表失败' });
    }

    // 构建树形结构
    const categoryTree = buildCategoryTree(data);
    res.json(categoryTree);
  } catch (error) {
    console.error('获取分类列表错误:', error);
    res.status(500).json({ error: '服务器内部错误' });
  }
});

// 获取分类详情
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const { data, error } = await supabase
      .from('categories')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      console.error('获取分类详情错误:', error);
      return res.status(404).json({ error: '分类不存在' });
    }

    res.json(data);
  } catch (error) {
    console.error('获取分类详情错误:', error);
    res.status(500).json({ error: '服务器内部错误' });
  }
});

// 获取分类下的商品
router.get('/:id/products', async (req, res) => {
  try {
    const { id } = req.params;
    const {
      page = 1,
      limit = 20,
      sort = 'created_at',
      order = 'desc'
    } = req.query;

    const offset = (page - 1) * limit;

    // 获取当前分类及其所有子分类的ID
    const categoryIds = await getCategoryWithChildren(id);

    // 构建查询
    let query = supabase
      .from('products')
      .select(`
        *,
        categories:category_id (
          id,
          name
        )
      `)
      .eq('status', 'active')
      .in('category_id', categoryIds);

    // 添加排序
    const orderDirection = order === 'desc' ? false : true;
    query = query.order(sort, { ascending: orderDirection });

    // 添加分页
    query = query.range(offset, offset + limit - 1);

    const { data, error } = await query;

    if (error) {
      console.error('获取分类商品错误:', error);
      return res.status(500).json({ error: '获取分类商品失败' });
    }

    // 获取总数
    const { count: totalCount } = await supabase
      .from('products')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'active')
      .in('category_id', categoryIds);

    res.json({
      data,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalCount,
        pages: Math.ceil(totalCount / limit)
      }
    });
  } catch (error) {
    console.error('获取分类商品错误:', error);
    res.status(500).json({ error: '服务器内部错误' });
  }
});

// 构建分类树形结构的辅助函数
function buildCategoryTree(categories) {
  const categoryMap = {};
  const rootCategories = [];

  // 创建分类映射
  categories.forEach(category => {
    categoryMap[category.id] = { ...category, children: [] };
  });

  // 构建树形结构
  categories.forEach(category => {
    if (category.parent_id) {
      // 子分类
      if (categoryMap[category.parent_id]) {
        categoryMap[category.parent_id].children.push(categoryMap[category.id]);
      }
    } else {
      // 根分类
      rootCategories.push(categoryMap[category.id]);
    }
  });

  return rootCategories;
}

// 获取分类及其所有子分类ID的辅助函数
async function getCategoryWithChildren(categoryId) {
  const categoryIds = [categoryId];

  // 获取所有分类
  const { data: allCategories } = await supabase
    .from('categories')
    .select('id, parent_id');

  // 递归查找子分类
  function findChildren(parentId) {
    const children = allCategories.filter(cat => cat.parent_id === parentId);
    children.forEach(child => {
      categoryIds.push(child.id);
      findChildren(child.id);
    });
  }

  findChildren(categoryId);
  return categoryIds;
}

module.exports = router;
