const express = require('express');
const router = express.Router();
const supabase = require('../config/supabase');

// 认证中间件
const authenticateUser = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader) {
      return res.status(401).json({ error: '未提供认证令牌' });
    }

    const token = authHeader.replace('Bearer ', '');
    
    // 验证令牌
    const { data: { user }, error } = await supabase.auth.getUser(token);

    if (error || !user) {
      return res.status(401).json({ error: '无效的认证令牌' });
    }

    req.user = user;
    next();
  } catch (error) {
    console.error('认证错误:', error);
    res.status(500).json({ error: '服务器内部错误' });
  }
};

// 获取用户收藏列表
router.get('/favorites', authenticateUser, async (req, res) => {
  try {
    const { page = 1, limit = 20 } = req.query;
    const offset = (page - 1) * limit;

    const { data, error } = await supabase
      .from('user_favorites')
      .select(`
        id,
        created_at,
        products (
          id,
          name,
          subtitle,
          price,
          original_price,
          images,
          rating,
          review_count,
          categories:category_id (
            id,
            name
          )
        )
      `)
      .eq('user_id', req.user.id)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('获取收藏列表错误:', error);
      return res.status(500).json({ error: '获取收藏列表失败' });
    }

    // 获取总数
    const { count: totalCount } = await supabase
      .from('user_favorites')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', req.user.id);

    res.json({
      data,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalCount,
        pages: Math.ceil(totalCount / limit)
      }
    });
  } catch (error) {
    console.error('获取收藏列表错误:', error);
    res.status(500).json({ error: '服务器内部错误' });
  }
});

// 添加商品到收藏
router.post('/favorites', authenticateUser, async (req, res) => {
  try {
    const { product_id } = req.body;

    if (!product_id) {
      return res.status(400).json({ error: '商品ID不能为空' });
    }

    // 检查商品是否存在
    const { data: product } = await supabase
      .from('products')
      .select('id')
      .eq('id', product_id)
      .eq('status', 'active')
      .single();

    if (!product) {
      return res.status(404).json({ error: '商品不存在' });
    }

    // 检查是否已收藏
    const { data: existingFavorite } = await supabase
      .from('user_favorites')
      .select('id')
      .eq('user_id', req.user.id)
      .eq('product_id', product_id)
      .single();

    if (existingFavorite) {
      return res.status(400).json({ error: '商品已在收藏列表中' });
    }

    // 添加收藏
    const { data, error } = await supabase
      .from('user_favorites')
      .insert([
        {
          user_id: req.user.id,
          product_id: product_id
        }
      ])
      .select()
      .single();

    if (error) {
      console.error('添加收藏错误:', error);
      return res.status(500).json({ error: '添加收藏失败' });
    }

    res.json({ message: '收藏成功', data });
  } catch (error) {
    console.error('添加收藏错误:', error);
    res.status(500).json({ error: '服务器内部错误' });
  }
});

// 取消收藏
router.delete('/favorites/:id', authenticateUser, async (req, res) => {
  try {
    const { id } = req.params;

    const { error } = await supabase
      .from('user_favorites')
      .delete()
      .eq('id', id)
      .eq('user_id', req.user.id);

    if (error) {
      console.error('取消收藏错误:', error);
      return res.status(500).json({ error: '取消收藏失败' });
    }

    res.json({ message: '取消收藏成功' });
  } catch (error) {
    console.error('取消收藏错误:', error);
    res.status(500).json({ error: '服务器内部错误' });
  }
});

// 获取浏览历史
router.get('/history', authenticateUser, async (req, res) => {
  try {
    const { page = 1, limit = 20 } = req.query;
    const offset = (page - 1) * limit;

    const { data, error } = await supabase
      .from('user_history')
      .select(`
        id,
        viewed_at,
        products (
          id,
          name,
          subtitle,
          price,
          original_price,
          images,
          rating,
          review_count,
          categories:category_id (
            id,
            name
          )
        )
      `)
      .eq('user_id', req.user.id)
      .order('viewed_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('获取浏览历史错误:', error);
      return res.status(500).json({ error: '获取浏览历史失败' });
    }

    // 获取总数
    const { count: totalCount } = await supabase
      .from('user_history')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', req.user.id);

    res.json({
      data,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalCount,
        pages: Math.ceil(totalCount / limit)
      }
    });
  } catch (error) {
    console.error('获取浏览历史错误:', error);
    res.status(500).json({ error: '服务器内部错误' });
  }
});

// 添加浏览历史
router.post('/history', authenticateUser, async (req, res) => {
  try {
    const { product_id } = req.body;

    if (!product_id) {
      return res.status(400).json({ error: '商品ID不能为空' });
    }

    // 检查商品是否存在
    const { data: product } = await supabase
      .from('products')
      .select('id')
      .eq('id', product_id)
      .eq('status', 'active')
      .single();

    if (!product) {
      return res.status(404).json({ error: '商品不存在' });
    }

    // 添加浏览历史
    const { data, error } = await supabase
      .from('user_history')
      .insert([
        {
          user_id: req.user.id,
          product_id: product_id
        }
      ])
      .select()
      .single();

    if (error) {
      console.error('添加浏览历史错误:', error);
      return res.status(500).json({ error: '添加浏览历史失败' });
    }

    res.json({ message: '添加浏览历史成功', data });
  } catch (error) {
    console.error('添加浏览历史错误:', error);
    res.status(500).json({ error: '服务器内部错误' });
  }
});

module.exports = router;
