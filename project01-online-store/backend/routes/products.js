const express = require('express');
const router = express.Router();
const supabase = require('../config/supabase');

// 获取商品列表（支持分页、筛选、排序）
router.get('/', async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      category,
      brand,
      min_price,
      max_price,
      sort = 'created_at',
      order = 'desc',
      search
    } = req.query;

    const offset = (page - 1) * limit;

    // 构建查询
    let query = supabase
      .from('products')
      .select(`
        *,
        categories:category_id (
          id,
          name
        )
      `)
      .eq('status', 'active');

    // 添加筛选条件
    if (category) {
      query = query.eq('category_id', category);
    }
    if (brand) {
      query = query.eq('brand', brand);
    }
    if (min_price) {
      query = query.gte('price', min_price);
    }
    if (max_price) {
      query = query.lte('price', max_price);
    }
    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%,brand.ilike.%${search}%`);
    }

    // 添加排序
    const orderDirection = order === 'desc' ? false : true;
    query = query.order(sort, { ascending: orderDirection });

    // 添加分页
    query = query.range(offset, offset + limit - 1);

    const { data, error } = await query;

    if (error) {
      console.error('获取商品列表错误:', error);
      return res.status(500).json({ error: '获取商品列表失败' });
    }

    // 获取总数
    const { count: totalCount } = await supabase
      .from('products')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'active');

    res.json({
      data,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalCount,
        pages: Math.ceil(totalCount / limit)
      }
    });
  } catch (error) {
    console.error('获取商品列表错误:', error);
    res.status(500).json({ error: '服务器内部错误' });
  }
});

// 搜索商品
router.get('/search', async (req, res) => {
  try {
    const {
      q: query,
      page = 1,
      limit = 20,
      category,
      sort = 'created_at',
      order = 'desc'
    } = req.query;

    if (!query) {
      return res.status(400).json({ error: '搜索关键词不能为空' });
    }

    const offset = (page - 1) * limit;

    // 构建搜索查询
    let searchQuery = supabase
      .from('products')
      .select(`
        *,
        categories:category_id (
          id,
          name
        )
      `)
      .eq('status', 'active')
      .or(`name.ilike.%${query}%,description.ilike.%${query}%,brand.ilike.%${query}%`);

    // 添加分类筛选
    if (category) {
      searchQuery = searchQuery.eq('category_id', category);
    }

    // 添加排序
    const orderDirection = order === 'desc' ? false : true;
    searchQuery = searchQuery.order(sort, { ascending: orderDirection });

    // 添加分页
    searchQuery = searchQuery.range(offset, offset + limit - 1);

    const { data, error } = await searchQuery;

    if (error) {
      console.error('搜索商品错误:', error);
      return res.status(500).json({ error: '搜索失败' });
    }

    // 获取搜索结果总数
    const { count: totalCount } = await supabase
      .from('products')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'active')
      .or(`name.ilike.%${query}%,description.ilike.%${query}%,brand.ilike.%${query}%`);

    res.json({
      data,
      query,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: totalCount,
        pages: Math.ceil(totalCount / limit)
      }
    });
  } catch (error) {
    console.error('搜索商品错误:', error);
    res.status(500).json({ error: '服务器内部错误' });
  }
});

// 获取商品详情
router.get('/:id', async (req, res) => {
  try {
    const { id } = req.params;

    const { data, error } = await supabase
      .from('products')
      .select(`
        *,
        categories:category_id (
          id,
          name,
          parent_id
        )
      `)
      .eq('id', id)
      .eq('status', 'active')
      .single();

    if (error) {
      console.error('获取商品详情错误:', error);
      return res.status(404).json({ error: '商品不存在' });
    }

    res.json(data);
  } catch (error) {
    console.error('获取商品详情错误:', error);
    res.status(500).json({ error: '服务器内部错误' });
  }
});

module.exports = router;
