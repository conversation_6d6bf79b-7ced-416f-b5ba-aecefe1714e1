!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define([],t):"object"==typeof exports?exports.supabase=t():e.supabase=t()}(self,(()=>(()=>{"use strict";var e={743:(e,t,r)=>{var o=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if(void 0!==r.g)return r.g;throw new Error("unable to locate global object")}();e.exports=t=o.fetch,o.fetch&&(t.default=o.fetch.bind(o)),t.Headers=o.Headers,t.Request=o.Request,t.Response=o.Response},274:function(e,t,r){var o=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.StorageClient=void 0;const n=o(r(981)),i=o(r(436));class s extends i.default{constructor(e,t={},r){super(e,t,r)}from(e){return new n.default(this.url,this.headers,e,this.fetch)}}t.StorageClient=s},341:function(e,t,r){var o=this&&this.__createBinding||(Object.create?function(e,t,r,o){void 0===o&&(o=r);var n=Object.getOwnPropertyDescriptor(t,r);n&&!("get"in n?!t.__esModule:n.writable||n.configurable)||(n={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,o,n)}:function(e,t,r,o){void 0===o&&(o=r),e[o]=t[r]}),n=this&&this.__exportStar||function(e,t){for(var r in e)"default"===r||Object.prototype.hasOwnProperty.call(t,r)||o(t,e,r)};Object.defineProperty(t,"__esModule",{value:!0}),t.StorageClient=void 0;var i=r(274);Object.defineProperty(t,"StorageClient",{enumerable:!0,get:function(){return i.StorageClient}}),n(r(717),t),n(r(752),t)},678:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DEFAULT_HEADERS=void 0;const o=r(506);t.DEFAULT_HEADERS={"X-Client-Info":`storage-js/${o.version}`}},752:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.StorageUnknownError=t.StorageApiError=t.isStorageError=t.StorageError=void 0;class r extends Error{constructor(e){super(e),this.__isStorageError=!0,this.name="StorageError"}}t.StorageError=r,t.isStorageError=function(e){return"object"==typeof e&&null!==e&&"__isStorageError"in e},t.StorageApiError=class extends r{constructor(e,t){super(e),this.name="StorageApiError",this.status=t}toJSON(){return{name:this.name,message:this.message,status:this.status}}},t.StorageUnknownError=class extends r{constructor(e,t){super(e),this.name="StorageUnknownError",this.originalError=t}}},716:function(e,t,r){var o=this&&this.__awaiter||function(e,t,r,o){return new(r||(r=Promise))((function(n,i){function s(e){try{u(o.next(e))}catch(e){i(e)}}function a(e){try{u(o.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(s,a)}u((o=o.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.remove=t.head=t.put=t.post=t.get=void 0;const n=r(752),i=r(610),s=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e);function a(e,t,r,a,u,c){return o(this,void 0,void 0,(function*(){return new Promise(((d,l)=>{e(r,((e,t,r,o)=>{const n={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?n:(n.headers=Object.assign({"Content-Type":"application/json"},null==t?void 0:t.headers),o&&(n.body=JSON.stringify(o)),Object.assign(Object.assign({},n),r))})(t,a,u,c)).then((e=>{if(!e.ok)throw e;return(null==a?void 0:a.noResolveJson)?e:e.json()})).then((e=>d(e))).catch((e=>((e,t,r)=>o(void 0,void 0,void 0,(function*(){const o=yield(0,i.resolveResponse)();e instanceof o&&!(null==r?void 0:r.noResolveJson)?e.json().then((r=>{t(new n.StorageApiError(s(r),e.status||500))})).catch((e=>{t(new n.StorageUnknownError(s(e),e))})):t(new n.StorageUnknownError(s(e),e))})))(e,l,a)))}))}))}t.get=function(e,t,r,n){return o(this,void 0,void 0,(function*(){return a(e,"GET",t,r,n)}))},t.post=function(e,t,r,n,i){return o(this,void 0,void 0,(function*(){return a(e,"POST",t,n,i,r)}))},t.put=function(e,t,r,n,i){return o(this,void 0,void 0,(function*(){return a(e,"PUT",t,n,i,r)}))},t.head=function(e,t,r,n){return o(this,void 0,void 0,(function*(){return a(e,"HEAD",t,Object.assign(Object.assign({},r),{noResolveJson:!0}),n)}))},t.remove=function(e,t,r,n,i){return o(this,void 0,void 0,(function*(){return a(e,"DELETE",t,n,i,r)}))}},610:function(e,t,r){var o=this&&this.__createBinding||(Object.create?function(e,t,r,o){void 0===o&&(o=r);var n=Object.getOwnPropertyDescriptor(t,r);n&&!("get"in n?!t.__esModule:n.writable||n.configurable)||(n={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,o,n)}:function(e,t,r,o){void 0===o&&(o=r),e[o]=t[r]}),n=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&o(t,e,r);return n(t,e),t},s=this&&this.__awaiter||function(e,t,r,o){return new(r||(r=Promise))((function(n,i){function s(e){try{u(o.next(e))}catch(e){i(e)}}function a(e){try{u(o.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(s,a)}u((o=o.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0}),t.recursiveToCamel=t.resolveResponse=t.resolveFetch=void 0,t.resolveFetch=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then((()=>i(r(743)))).then((({default:t})=>t(...e))):fetch),(...e)=>t(...e)},t.resolveResponse=()=>s(void 0,void 0,void 0,(function*(){return"undefined"==typeof Response?(yield Promise.resolve().then((()=>i(r(743))))).Response:Response})),t.recursiveToCamel=e=>{if(Array.isArray(e))return e.map((e=>(0,t.recursiveToCamel)(e)));if("function"==typeof e||e!==Object(e))return e;const r={};return Object.entries(e).forEach((([e,o])=>{const n=e.replace(/([-_][a-z])/gi,(e=>e.toUpperCase().replace(/[-_]/g,"")));r[n]=(0,t.recursiveToCamel)(o)})),r}},717:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0})},506:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.version=void 0,t.version="0.0.0"},436:function(e,t,r){var o=this&&this.__awaiter||function(e,t,r,o){return new(r||(r=Promise))((function(n,i){function s(e){try{u(o.next(e))}catch(e){i(e)}}function a(e){try{u(o.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(s,a)}u((o=o.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0});const n=r(678),i=r(752),s=r(716),a=r(610);t.default=class{constructor(e,t={},r){this.url=e,this.headers=Object.assign(Object.assign({},n.DEFAULT_HEADERS),t),this.fetch=(0,a.resolveFetch)(r)}listBuckets(){return o(this,void 0,void 0,(function*(){try{return{data:yield(0,s.get)(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(e){if((0,i.isStorageError)(e))return{data:null,error:e};throw e}}))}getBucket(e){return o(this,void 0,void 0,(function*(){try{return{data:yield(0,s.get)(this.fetch,`${this.url}/bucket/${e}`,{headers:this.headers}),error:null}}catch(e){if((0,i.isStorageError)(e))return{data:null,error:e};throw e}}))}createBucket(e,t={public:!1}){return o(this,void 0,void 0,(function*(){try{return{data:yield(0,s.post)(this.fetch,`${this.url}/bucket`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(e){if((0,i.isStorageError)(e))return{data:null,error:e};throw e}}))}updateBucket(e,t){return o(this,void 0,void 0,(function*(){try{return{data:yield(0,s.put)(this.fetch,`${this.url}/bucket/${e}`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(e){if((0,i.isStorageError)(e))return{data:null,error:e};throw e}}))}emptyBucket(e){return o(this,void 0,void 0,(function*(){try{return{data:yield(0,s.post)(this.fetch,`${this.url}/bucket/${e}/empty`,{},{headers:this.headers}),error:null}}catch(e){if((0,i.isStorageError)(e))return{data:null,error:e};throw e}}))}deleteBucket(e){return o(this,void 0,void 0,(function*(){try{return{data:yield(0,s.remove)(this.fetch,`${this.url}/bucket/${e}`,{},{headers:this.headers}),error:null}}catch(e){if((0,i.isStorageError)(e))return{data:null,error:e};throw e}}))}}},981:function(e,t,r){var o=this&&this.__awaiter||function(e,t,r,o){return new(r||(r=Promise))((function(n,i){function s(e){try{u(o.next(e))}catch(e){i(e)}}function a(e){try{u(o.throw(e))}catch(e){i(e)}}function u(e){var t;e.done?n(e.value):(t=e.value,t instanceof r?t:new r((function(e){e(t)}))).then(s,a)}u((o=o.apply(e,t||[])).next())}))};Object.defineProperty(t,"__esModule",{value:!0});const n=r(752),i=r(716),s=r(610),a={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},u={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};t.default=class{constructor(e,t={},r,o){this.url=e,this.headers=t,this.bucketId=r,this.fetch=(0,s.resolveFetch)(o)}uploadOrUpdate(e,t,r,i){return o(this,void 0,void 0,(function*(){try{let o;const n=Object.assign(Object.assign({},u),i);let s=Object.assign(Object.assign({},this.headers),"POST"===e&&{"x-upsert":String(n.upsert)});const a=n.metadata;"undefined"!=typeof Blob&&r instanceof Blob?(o=new FormData,o.append("cacheControl",n.cacheControl),a&&o.append("metadata",this.encodeMetadata(a)),o.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?(o=r,o.append("cacheControl",n.cacheControl),a&&o.append("metadata",this.encodeMetadata(a))):(o=r,s["cache-control"]=`max-age=${n.cacheControl}`,s["content-type"]=n.contentType,a&&(s["x-metadata"]=this.toBase64(this.encodeMetadata(a)))),(null==i?void 0:i.headers)&&(s=Object.assign(Object.assign({},s),i.headers));const c=this._removeEmptyFolders(t),d=this._getFinalPath(c),l=yield this.fetch(`${this.url}/object/${d}`,Object.assign({method:e,body:o,headers:s},(null==n?void 0:n.duplex)?{duplex:n.duplex}:{})),h=yield l.json();return l.ok?{data:{path:c,id:h.Id,fullPath:h.Key},error:null}:{data:null,error:h}}catch(e){if((0,n.isStorageError)(e))return{data:null,error:e};throw e}}))}upload(e,t,r){return o(this,void 0,void 0,(function*(){return this.uploadOrUpdate("POST",e,t,r)}))}uploadToSignedUrl(e,t,r,i){return o(this,void 0,void 0,(function*(){const o=this._removeEmptyFolders(e),s=this._getFinalPath(o),a=new URL(this.url+`/object/upload/sign/${s}`);a.searchParams.set("token",t);try{let e;const t=Object.assign({upsert:u.upsert},i),n=Object.assign(Object.assign({},this.headers),{"x-upsert":String(t.upsert)});"undefined"!=typeof Blob&&r instanceof Blob?(e=new FormData,e.append("cacheControl",t.cacheControl),e.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?(e=r,e.append("cacheControl",t.cacheControl)):(e=r,n["cache-control"]=`max-age=${t.cacheControl}`,n["content-type"]=t.contentType);const s=yield this.fetch(a.toString(),{method:"PUT",body:e,headers:n}),c=yield s.json();return s.ok?{data:{path:o,fullPath:c.Key},error:null}:{data:null,error:c}}catch(e){if((0,n.isStorageError)(e))return{data:null,error:e};throw e}}))}createSignedUploadUrl(e,t){return o(this,void 0,void 0,(function*(){try{let r=this._getFinalPath(e);const o=Object.assign({},this.headers);(null==t?void 0:t.upsert)&&(o["x-upsert"]="true");const s=yield(0,i.post)(this.fetch,`${this.url}/object/upload/sign/${r}`,{},{headers:o}),a=new URL(this.url+s.url),u=a.searchParams.get("token");if(!u)throw new n.StorageError("No token returned by API");return{data:{signedUrl:a.toString(),path:e,token:u},error:null}}catch(e){if((0,n.isStorageError)(e))return{data:null,error:e};throw e}}))}update(e,t,r){return o(this,void 0,void 0,(function*(){return this.uploadOrUpdate("PUT",e,t,r)}))}move(e,t,r){return o(this,void 0,void 0,(function*(){try{return{data:yield(0,i.post)(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==r?void 0:r.destinationBucket},{headers:this.headers}),error:null}}catch(e){if((0,n.isStorageError)(e))return{data:null,error:e};throw e}}))}copy(e,t,r){return o(this,void 0,void 0,(function*(){try{return{data:{path:(yield(0,i.post)(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==r?void 0:r.destinationBucket},{headers:this.headers})).Key},error:null}}catch(e){if((0,n.isStorageError)(e))return{data:null,error:e};throw e}}))}createSignedUrl(e,t,r){return o(this,void 0,void 0,(function*(){try{let o=this._getFinalPath(e),n=yield(0,i.post)(this.fetch,`${this.url}/object/sign/${o}`,Object.assign({expiresIn:t},(null==r?void 0:r.transform)?{transform:r.transform}:{}),{headers:this.headers});const s=(null==r?void 0:r.download)?`&download=${!0===r.download?"":r.download}`:"";return n={signedUrl:encodeURI(`${this.url}${n.signedURL}${s}`)},{data:n,error:null}}catch(e){if((0,n.isStorageError)(e))return{data:null,error:e};throw e}}))}createSignedUrls(e,t,r){return o(this,void 0,void 0,(function*(){try{const o=yield(0,i.post)(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:t,paths:e},{headers:this.headers}),n=(null==r?void 0:r.download)?`&download=${!0===r.download?"":r.download}`:"";return{data:o.map((e=>Object.assign(Object.assign({},e),{signedUrl:e.signedURL?encodeURI(`${this.url}${e.signedURL}${n}`):null}))),error:null}}catch(e){if((0,n.isStorageError)(e))return{data:null,error:e};throw e}}))}download(e,t){return o(this,void 0,void 0,(function*(){const r=void 0!==(null==t?void 0:t.transform)?"render/image/authenticated":"object",o=this.transformOptsToQueryString((null==t?void 0:t.transform)||{}),s=o?`?${o}`:"";try{const t=this._getFinalPath(e),o=yield(0,i.get)(this.fetch,`${this.url}/${r}/${t}${s}`,{headers:this.headers,noResolveJson:!0});return{data:yield o.blob(),error:null}}catch(e){if((0,n.isStorageError)(e))return{data:null,error:e};throw e}}))}info(e){return o(this,void 0,void 0,(function*(){const t=this._getFinalPath(e);try{const e=yield(0,i.get)(this.fetch,`${this.url}/object/info/${t}`,{headers:this.headers});return{data:(0,s.recursiveToCamel)(e),error:null}}catch(e){if((0,n.isStorageError)(e))return{data:null,error:e};throw e}}))}exists(e){return o(this,void 0,void 0,(function*(){const t=this._getFinalPath(e);try{return yield(0,i.head)(this.fetch,`${this.url}/object/${t}`,{headers:this.headers}),{data:!0,error:null}}catch(e){if((0,n.isStorageError)(e)&&e instanceof n.StorageUnknownError){const t=e.originalError;if([400,404].includes(null==t?void 0:t.status))return{data:!1,error:e}}throw e}}))}getPublicUrl(e,t){const r=this._getFinalPath(e),o=[],n=(null==t?void 0:t.download)?`download=${!0===t.download?"":t.download}`:"";""!==n&&o.push(n);const i=void 0!==(null==t?void 0:t.transform)?"render/image":"object",s=this.transformOptsToQueryString((null==t?void 0:t.transform)||{});""!==s&&o.push(s);let a=o.join("&");return""!==a&&(a=`?${a}`),{data:{publicUrl:encodeURI(`${this.url}/${i}/public/${r}${a}`)}}}remove(e){return o(this,void 0,void 0,(function*(){try{return{data:yield(0,i.remove)(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:e},{headers:this.headers}),error:null}}catch(e){if((0,n.isStorageError)(e))return{data:null,error:e};throw e}}))}list(e,t,r){return o(this,void 0,void 0,(function*(){try{const o=Object.assign(Object.assign(Object.assign({},a),t),{prefix:e||""});return{data:yield(0,i.post)(this.fetch,`${this.url}/object/list/${this.bucketId}`,o,{headers:this.headers},r),error:null}}catch(e){if((0,n.isStorageError)(e))return{data:null,error:e};throw e}}))}encodeMetadata(e){return JSON.stringify(e)}toBase64(e){return"undefined"!=typeof Buffer?Buffer.from(e).toString("base64"):btoa(e)}_getFinalPath(e){return`${this.bucketId}/${e}`}_removeEmptyFolders(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(e){const t=[];return e.width&&t.push(`width=${e.width}`),e.height&&t.push(`height=${e.height}`),e.resize&&t.push(`resize=${e.resize}`),e.format&&t.push(`format=${e.format}`),e.quality&&t.push(`quality=${e.quality}`),t.join("&")}}}},t={};function r(o){var n=t[o];if(void 0!==n)return n.exports;var i=t[o]={exports:{}};return e[o].call(i.exports,i,i.exports,r),i.exports}return r.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),r(341)})()));