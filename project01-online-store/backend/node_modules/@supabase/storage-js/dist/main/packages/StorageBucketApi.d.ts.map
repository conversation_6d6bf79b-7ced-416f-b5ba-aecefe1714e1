{"version": 3, "file": "StorageBucketApi.d.ts", "sourceRoot": "", "sources": ["../../../src/packages/StorageBucketApi.ts"], "names": [], "mappings": "AACA,OAAO,EAAkB,YAAY,EAAE,MAAM,eAAe,CAAA;AAC5D,OAAO,EAAE,KAAK,EAA0B,MAAM,cAAc,CAAA;AAE5D,OAAO,EAAE,MAAM,EAAE,MAAM,cAAc,CAAA;AAErC,MAAM,CAAC,OAAO,OAAO,gBAAgB;IACnC,SAAS,CAAC,GAAG,EAAE,MAAM,CAAA;IACrB,SAAS,CAAC,OAAO,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE,CAAA;IAC5C,SAAS,CAAC,KAAK,EAAE,KAAK,CAAA;gBAEV,GAAG,EAAE,MAAM,EAAE,OAAO,GAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;KAAO,EAAE,KAAK,CAAC,EAAE,KAAK;IAM/E;;OAEG;IACG,WAAW,IAAI,OAAO,CACxB;QACE,IAAI,EAAE,MAAM,EAAE,CAAA;QACd,KAAK,EAAE,IAAI,CAAA;KACZ,GACD;QACE,IAAI,EAAE,IAAI,CAAA;QACV,KAAK,EAAE,YAAY,CAAA;KACpB,CACJ;IAaD;;;;OAIG;IACG,SAAS,CACb,EAAE,EAAE,MAAM,GACT,OAAO,CACN;QACE,IAAI,EAAE,MAAM,CAAA;QACZ,KAAK,EAAE,IAAI,CAAA;KACZ,GACD;QACE,IAAI,EAAE,IAAI,CAAA;QACV,KAAK,EAAE,YAAY,CAAA;KACpB,CACJ;IAaD;;;;;;;;;;;;OAYG;IACG,YAAY,CAChB,EAAE,EAAE,MAAM,EACV,OAAO,GAAE;QACP,MAAM,EAAE,OAAO,CAAA;QACf,aAAa,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI,CAAA;QACtC,gBAAgB,CAAC,EAAE,MAAM,EAAE,GAAG,IAAI,CAAA;KAGnC,GACA,OAAO,CACN;QACE,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,MAAM,CAAC,CAAA;QAC1B,KAAK,EAAE,IAAI,CAAA;KACZ,GACD;QACE,IAAI,EAAE,IAAI,CAAA;QACV,KAAK,EAAE,YAAY,CAAA;KACpB,CACJ;IAwBD;;;;;;;;;;;OAWG;IACG,YAAY,CAChB,EAAE,EAAE,MAAM,EACV,OAAO,EAAE;QACP,MAAM,EAAE,OAAO,CAAA;QACf,aAAa,CAAC,EAAE,MAAM,GAAG,MAAM,GAAG,IAAI,CAAA;QACtC,gBAAgB,CAAC,EAAE,MAAM,EAAE,GAAG,IAAI,CAAA;KACnC,GACA,OAAO,CACN;QACE,IAAI,EAAE;YAAE,OAAO,EAAE,MAAM,CAAA;SAAE,CAAA;QACzB,KAAK,EAAE,IAAI,CAAA;KACZ,GACD;QACE,IAAI,EAAE,IAAI,CAAA;QACV,KAAK,EAAE,YAAY,CAAA;KACpB,CACJ;IAwBD;;;;OAIG;IACG,WAAW,CACf,EAAE,EAAE,MAAM,GACT,OAAO,CACN;QACE,IAAI,EAAE;YAAE,OAAO,EAAE,MAAM,CAAA;SAAE,CAAA;QACzB,KAAK,EAAE,IAAI,CAAA;KACZ,GACD;QACE,IAAI,EAAE,IAAI,CAAA;QACV,KAAK,EAAE,YAAY,CAAA;KACpB,CACJ;IAkBD;;;;;OAKG;IACG,YAAY,CAChB,EAAE,EAAE,MAAM,GACT,OAAO,CACN;QACE,IAAI,EAAE;YAAE,OAAO,EAAE,MAAM,CAAA;SAAE,CAAA;QACzB,KAAK,EAAE,IAAI,CAAA;KACZ,GACD;QACE,IAAI,EAAE,IAAI,CAAA;QACV,KAAK,EAAE,YAAY,CAAA;KACpB,CACJ;CAiBF"}