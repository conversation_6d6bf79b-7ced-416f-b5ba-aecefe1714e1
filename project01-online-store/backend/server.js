const express = require('express');
const cors = require('cors');
require('dotenv').config();

const app = express();
const PORT = process.env.PORT || 5000;

// 中间件
app.use(cors());
app.use(express.json());

// 路由
app.use('/api/products', require('./routes/products'));
app.use('/api/categories', require('./routes/categories'));
// app.use('/api/auth', require('./routes/auth'));
// app.use('/api/user', require('./routes/user'));

// 健康检查
app.get('/health', (req, res) => {
  res.json({ status: 'OK', message: '在线商品展示平台 API 服务正常运行' });
});

// 错误处理中间件
app.use((err, req, res, next) => {
  console.error(err.stack);
  res.status(500).json({ error: '服务器内部错误' });
});

// 404 处理
app.use('*', (req, res) => {
  res.status(404).json({ error: '接口不存在' });
});

app.listen(PORT, () => {
  console.log(`服务器运行在端口 ${PORT}`);
});
