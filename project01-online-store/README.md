# 在线商品展示平台 - 项目一

## 项目概述

这是一个基于React + Node.js + Supabase的在线商品展示平台，专门设计用作爬虫实战项目的目标网站。该项目实现了完整的商品展示、搜索、分类等功能，为后续的爬虫项目提供了丰富的数据结构和多层级页面。

## 技术栈

### 前端
- **React 18+**: 现代化前端框架
- **React Router v6**: 客户端路由
- **Axios**: HTTP请求库
- **Vite**: 构建工具
- **CSS3**: 样式设计

### 后端
- **Node.js**: 服务器运行时
- **Express.js**: Web框架
- **Supabase**: 数据库和认证服务
- **CORS**: 跨域资源共享

### 数据库
- **PostgreSQL**: 通过Supabase提供
- **实时数据同步**: Supabase Realtime

## 项目结构

```
project01-online-store/
├── docs/                          # 项目文档
│   ├── 01-需求分析报告.md
│   ├── 02-产品需求文档PRD.md
│   ├── 03-系统架构设计文档.md
│   └── 04-数据库设计文档.md
├── database/                      # 数据库脚本
│   └── init.sql
├── backend/                       # 后端代码
│   ├── config/
│   │   └── supabase.js           # Supabase配置
│   ├── routes/                   # API路由
│   │   ├── products-simple.js   # 商品API
│   │   ├── categories.js         # 分类API
│   │   ├── auth.js              # 认证API
│   │   └── user.js              # 用户API
│   ├── .env                     # 环境变量
│   ├── server.js                # 服务器入口
│   └── package.json
└── frontend/                     # 前端代码
    ├── src/
    │   ├── components/           # React组件
    │   │   ├── common/          # 通用组件
    │   │   └── product/         # 商品组件
    │   ├── pages/               # 页面组件
    │   ├── services/            # API服务
    │   ├── App.jsx              # 主应用组件
    │   └── main.jsx             # 应用入口
    ├── public/
    └── package.json
```

## 功能特性

### 核心功能
1. **商品展示系统**
   - 商品列表展示（支持分页）
   - 商品详情页面
   - 商品图片展示
   - 价格和库存信息

2. **搜索和筛选**
   - 关键词搜索
   - 价格范围筛选
   - 品牌筛选
   - 多种排序方式

3. **分类浏览**
   - 商品分类展示
   - 分类商品列表
   - 多级分类支持

4. **响应式设计**
   - 桌面端和移动端适配
   - 现代化UI设计

### 爬虫友好特性
1. **丰富的数据结构**
   - 结构化的商品信息
   - JSON格式的API响应
   - 多层级页面结构

2. **多样化的URL模式**
   - RESTful API设计
   - 分页参数
   - 搜索和筛选参数

3. **适中的数据量**
   - 适合爬虫练习的数据规模
   - 真实的电商网站结构

## 快速开始

### 环境要求
- Node.js 18+
- npm 或 yarn

### 安装和运行

1. **克隆项目**
```bash
cd project01-online-store
```

2. **安装后端依赖**
```bash
cd backend
npm install
```

3. **配置环境变量**
```bash
# 复制 .env 文件并配置Supabase信息
cp .env.example .env
```

4. **启动后端服务**
```bash
npm run dev
# 服务器将运行在 http://localhost:3001
```

5. **安装前端依赖**
```bash
cd ../frontend
npm install
```

6. **启动前端服务**
```bash
npm run dev
# 前端将运行在 http://localhost:5173
```

### 数据库初始化

项目使用Supabase作为数据库服务，数据库表结构和初始数据已经配置完成。

## API接口

### 商品相关
- `GET /api/products` - 获取商品列表
- `GET /api/products/:id` - 获取商品详情
- `GET /api/products/search` - 搜索商品

### 分类相关
- `GET /api/categories` - 获取分类列表
- `GET /api/categories/:id/products` - 获取分类下的商品

## 页面结构

### 主要页面
1. **首页** (`/`) - 展示特色商品和分类导航
2. **商品列表页** (`/products`) - 展示所有商品，支持筛选和分页
3. **商品详情页** (`/products/:id`) - 展示单个商品的详细信息
4. **搜索结果页** (`/search`) - 展示搜索结果

### 爬虫练习建议
1. **基础爬取**: 从首页开始，爬取商品列表
2. **深度爬取**: 进入商品详情页获取完整信息
3. **搜索爬取**: 使用搜索功能获取特定商品
4. **分页处理**: 处理商品列表的分页数据
5. **数据清洗**: 处理价格、图片等结构化数据

## 项目特点

### 适合爬虫学习的特性
1. **清晰的HTML结构**: 使用语义化标签和CSS类名
2. **RESTful API**: 标准的API接口设计
3. **分页数据**: 真实的分页场景
4. **动态内容**: 部分内容通过Ajax加载
5. **多媒体内容**: 图片、价格等多样化数据

### 技术亮点
1. **现代化技术栈**: 使用最新的React和Node.js技术
2. **响应式设计**: 适配多种设备
3. **性能优化**: 图片懒加载、代码分割等
4. **错误处理**: 完善的错误处理机制

## 后续扩展

这个项目将作为项目二的基础，在项目二中将会添加：
1. **高级登录验证**: 复杂的用户认证系统
2. **安全增强**: 验证码、反爬虫机制
3. **功能增强**: 更多交互功能
4. **性能优化**: 缓存、CDN等

## 许可证

MIT License

## 联系方式

如有问题，请联系项目维护者。
