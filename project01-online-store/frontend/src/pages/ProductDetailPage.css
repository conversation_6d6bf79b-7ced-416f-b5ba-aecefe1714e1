.product-detail-page {
  padding: 40px 0;
  min-height: 100vh;
}

.product-detail {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 60px;
  margin-bottom: 80px;
}

/* 商品图片区域 */
.product-images {
  position: sticky;
  top: 100px;
  height: fit-content;
}

.main-image {
  position: relative;
  width: 100%;
  height: 500px;
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 20px;
  background: #f8fafc;
}

.main-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.discount-badge {
  position: absolute;
  top: 20px;
  right: 20px;
  background: #ef4444;
  color: white;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: bold;
}

.image-thumbnails {
  display: flex;
  gap: 10px;
  overflow-x: auto;
  padding: 10px 0;
}

.thumbnail {
  width: 80px;
  height: 80px;
  border-radius: 8px;
  object-fit: cover;
  cursor: pointer;
  border: 2px solid transparent;
  transition: border-color 0.2s;
}

.thumbnail:hover {
  border-color: #2563eb;
}

.thumbnail.active {
  border-color: #2563eb;
}

/* 商品信息区域 */
.product-info {
  padding: 20px 0;
}

.breadcrumb {
  color: #6b7280;
  font-size: 14px;
  margin-bottom: 15px;
}

.product-title {
  font-size: 32px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 10px;
  line-height: 1.3;
}

.product-subtitle {
  font-size: 18px;
  color: #6b7280;
  margin-bottom: 20px;
  line-height: 1.5;
}

.product-rating {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 25px;
}

.stars {
  display: flex;
  gap: 2px;
}

.star {
  color: #d1d5db;
  font-size: 18px;
}

.star.filled {
  color: #fbbf24;
}

.rating-text {
  font-size: 14px;
  color: #6b7280;
}

.product-price {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 30px;
}

.current-price {
  font-size: 36px;
  font-weight: 700;
  color: #ef4444;
}

.original-price {
  font-size: 24px;
  color: #9ca3af;
  text-decoration: line-through;
}

.product-meta {
  background: #f8fafc;
  padding: 20px;
  border-radius: 12px;
  margin-bottom: 30px;
}

.meta-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 0;
  border-bottom: 1px solid #e5e7eb;
}

.meta-item:last-child {
  border-bottom: none;
}

.meta-item .label {
  font-weight: 600;
  color: #374151;
}

.meta-item .value {
  color: #6b7280;
}

.product-description {
  margin-bottom: 30px;
}

.product-description h3 {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 15px;
}

.product-description p {
  color: #6b7280;
  line-height: 1.7;
  font-size: 16px;
}

.product-specifications {
  margin-bottom: 30px;
}

.product-specifications h3 {
  font-size: 20px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 15px;
}

.specs-grid {
  background: #f8fafc;
  padding: 20px;
  border-radius: 12px;
}

.spec-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #e5e7eb;
}

.spec-item:last-child {
  border-bottom: none;
}

.spec-label {
  font-weight: 600;
  color: #374151;
}

.spec-value {
  color: #6b7280;
}

/* 推荐商品区域 */
.recommendations {
  margin-top: 80px;
}

.recommendations h2 {
  font-size: 28px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 40px;
  text-align: center;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 30px;
}

/* 加载和错误状态 */
.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading {
  font-size: 18px;
  color: #6b7280;
}

.error {
  font-size: 18px;
  color: #ef4444;
  background: #fef2f2;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #fecaca;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .product-detail {
    grid-template-columns: 1fr;
    gap: 40px;
  }
  
  .product-images {
    position: static;
  }
  
  .main-image {
    height: 400px;
  }
}

@media (max-width: 768px) {
  .product-detail-page {
    padding: 20px 0;
  }
  
  .product-detail {
    gap: 30px;
    margin-bottom: 60px;
  }
  
  .main-image {
    height: 300px;
  }
  
  .product-title {
    font-size: 24px;
  }
  
  .product-subtitle {
    font-size: 16px;
  }
  
  .current-price {
    font-size: 28px;
  }
  
  .original-price {
    font-size: 20px;
  }
  
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
  }
  
  .recommendations {
    margin-top: 60px;
  }
  
  .recommendations h2 {
    font-size: 24px;
    margin-bottom: 30px;
  }
}
