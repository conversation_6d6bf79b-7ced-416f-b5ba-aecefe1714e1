import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import productService from '../services/productService';
import ProductCard from '../components/product/ProductCard';
import './HomePage.css';

const HomePage = () => {
  const [featuredProducts, setFeaturedProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchFeaturedProducts();
  }, []);

  const fetchFeaturedProducts = async () => {
    try {
      setLoading(true);
      const response = await productService.getProducts({ limit: 8 });
      setFeaturedProducts(response.data || []);
    } catch (err) {
      setError('获取商品数据失败');
      console.error('获取特色商品失败:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading">加载中...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="error-container">
        <div className="error">{error}</div>
      </div>
    );
  }

  return (
    <div className="home-page">
      {/* 英雄区域 */}
      <section className="hero">
        <div className="container">
          <div className="hero-content">
            <h1>欢迎来到在线商城</h1>
            <p>发现优质商品，享受购物乐趣</p>
            <Link to="/products" className="cta-button">
              浏览商品
            </Link>
          </div>
        </div>
      </section>

      {/* 商品分类 */}
      <section className="categories">
        <div className="container">
          <h2>商品分类</h2>
          <div className="category-grid">
            <Link to="/products?category=electronics" className="category-card">
              <div className="category-icon">📱</div>
              <h3>电子产品</h3>
              <p>手机、电脑、数码产品</p>
            </Link>
            <Link to="/products?category=clothing" className="category-card">
              <div className="category-icon">👕</div>
              <h3>服装鞋帽</h3>
              <p>时尚服装、鞋子、配饰</p>
            </Link>
            <Link to="/products?category=home" className="category-card">
              <div className="category-icon">🏠</div>
              <h3>家居用品</h3>
              <p>家具、装饰、生活用品</p>
            </Link>
            <Link to="/products?category=books" className="category-card">
              <div className="category-icon">📚</div>
              <h3>图书音像</h3>
              <p>图书、音乐、影视</p>
            </Link>
          </div>
        </div>
      </section>

      {/* 特色商品 */}
      <section className="featured-products">
        <div className="container">
          <div className="section-header">
            <h2>特色商品</h2>
            <Link to="/products" className="view-all">查看全部</Link>
          </div>
          
          {featuredProducts.length > 0 ? (
            <div className="products-grid">
              {featuredProducts.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          ) : (
            <div className="no-products">
              <p>暂无商品数据</p>
            </div>
          )}
        </div>
      </section>
    </div>
  );
};

export default HomePage;
