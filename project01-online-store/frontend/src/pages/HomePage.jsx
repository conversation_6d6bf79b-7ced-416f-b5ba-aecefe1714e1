import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import productService from '../services/productService';
import ProductCard from '../components/product/ProductCard';
import './HomePage.css';

const HomePage = () => {
  const [featuredProducts, setFeaturedProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    fetchFeaturedProducts();
  }, []);

  const fetchFeaturedProducts = async () => {
    try {
      setLoading(true);
      const response = await productService.getProducts({ limit: 8 });
      setFeaturedProducts(response.data || []);
    } catch (err) {
      setError('获取商品数据失败');
      console.error('获取特色商品失败:', err);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading">加载中...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="error-container">
        <div className="error">{error}</div>
      </div>
    );
  }

  return (
    <div className="home-page">
      {/* 英雄区域 */}
      <section className="hero">
        <div className="container">
          <div className="hero-content">
            <h1>欢迎来到在线商城</h1>
            <p>发现优质商品，享受购物乐趣。我们为您精选全球好物，让购物成为一种享受。</p>
            <div className="hero-buttons">
              <Link to="/products" className="cta-button primary">
                立即购物
              </Link>
              <Link to="/categories" className="cta-button secondary">
                浏览分类
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* 统计数据 */}
      <section className="stats">
        <div className="container">
          <div className="stats-grid">
            <div className="stat-item">
              <div className="stat-number">1000+</div>
              <div className="stat-label">优质商品</div>
            </div>
            <div className="stat-item">
              <div className="stat-number">50+</div>
              <div className="stat-label">知名品牌</div>
            </div>
            <div className="stat-item">
              <div className="stat-number">10000+</div>
              <div className="stat-label">满意客户</div>
            </div>
            <div className="stat-item">
              <div className="stat-number">24/7</div>
              <div className="stat-label">客户服务</div>
            </div>
          </div>
        </div>
      </section>

      {/* 商品分类 */}
      <section className="categories">
        <div className="container">
          <h2>商品分类</h2>
          <p className="subtitle">探索我们精心挑选的商品类别</p>
          <div className="category-grid">
            <Link to="/categories/1c55ba43-7024-4062-af63-accb5a19ca17" className="category-card">
              <div className="category-icon">📱</div>
              <h3>电子产品</h3>
              <p>手机、电脑、数码产品</p>
            </Link>
            <Link to="/categories/3ee1ae23-59a0-4369-9d28-0bb727752170" className="category-card">
              <div className="category-icon">👕</div>
              <h3>服装鞋帽</h3>
              <p>时尚服装、鞋子、配饰</p>
            </Link>
            <Link to="/categories/ad05a00a-b525-46df-829c-0b0e79fa3a9c" className="category-card">
              <div className="category-icon">🏠</div>
              <h3>家居用品</h3>
              <p>家具、装饰、生活用品</p>
            </Link>
            <Link to="/categories/4e7e037e-d4c0-4296-b546-fd13ed6cb3f4" className="category-card">
              <div className="category-icon">📚</div>
              <h3>图书音像</h3>
              <p>图书、音乐、影视</p>
            </Link>
          </div>
        </div>
      </section>

      {/* 特色商品 */}
      <section className="featured-products">
        <div className="container">
          <div className="section-header">
            <h2>特色商品</h2>
            <Link to="/products" className="view-all">查看全部</Link>
          </div>
          
          {featuredProducts.length > 0 ? (
            <div className="products-grid">
              {featuredProducts.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          ) : (
            <div className="no-products">
              <p>暂无商品数据</p>
            </div>
          )}
        </div>
      </section>
    </div>
  );
};

export default HomePage;
