.home-page {
  min-height: 100vh;
  background: #fafbfc;
}

/* 英雄区域 */
.hero {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-size: cover;
  background-attachment: fixed;
  color: white;
  padding: 120px 0 100px;
  text-align: center;
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.1);
  z-index: 1;
}

.hero-content {
  position: relative;
  z-index: 2;
  max-width: 800px;
  margin: 0 auto;
  padding: 0 20px;
}

.hero-content h1 {
  font-size: 64px;
  font-weight: 800;
  margin-bottom: 24px;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  line-height: 1.1;
  letter-spacing: -0.02em;
}

.hero-content p {
  font-size: 24px;
  margin-bottom: 50px;
  opacity: 0.95;
  font-weight: 300;
  line-height: 1.6;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.hero-buttons {
  display: flex;
  gap: 20px;
  justify-content: center;
  align-items: center;
}

.cta-button {
  display: inline-block;
  padding: 18px 40px;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 18px;
  transition: all 0.3s ease;
  border: none;
  cursor: pointer;
  min-width: 160px;
  text-align: center;
}

.cta-button.primary {
  background: linear-gradient(45deg, #ff6b6b, #ee5a24);
  color: white;
  box-shadow: 0 8px 25px rgba(238, 90, 36, 0.3);
}

.cta-button.primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 15px 35px rgba(238, 90, 36, 0.4);
  background: linear-gradient(45deg, #ee5a24, #ff6b6b);
}

.cta-button.secondary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 2px solid rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(10px);
}

.cta-button.secondary:hover {
  background: rgba(255, 255, 255, 0.3);
  border-color: rgba(255, 255, 255, 0.5);
  transform: translateY(-3px);
}

/* 统计数据区域 */
.stats {
  padding: 80px 0;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 60px;
  max-width: 1000px;
  margin: 0 auto;
}

.stat-item {
  text-align: center;
  padding: 30px 20px;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.stat-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.8);
}

.stat-number {
  font-size: 48px;
  font-weight: 800;
  color: #667eea;
  margin-bottom: 10px;
  letter-spacing: -0.02em;
}

.stat-label {
  font-size: 16px;
  color: #64748b;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* 分类区域 */
.categories {
  padding: 100px 0;
  background: linear-gradient(180deg, #ffffff 0%, #f8fafc 100%);
  position: relative;
}

.categories::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, #e2e8f0, transparent);
}

.categories h2 {
  text-align: center;
  font-size: 48px;
  font-weight: 800;
  color: #1a202c;
  margin-bottom: 20px;
  letter-spacing: -0.02em;
}

.categories .subtitle {
  text-align: center;
  font-size: 20px;
  color: #64748b;
  margin-bottom: 60px;
  font-weight: 300;
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 40px;
  max-width: 1200px;
  margin: 0 auto;
}

.category-card {
  background: white;
  padding: 50px 30px;
  border-radius: 20px;
  text-align: center;
  text-decoration: none;
  color: inherit;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.08);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  border: 1px solid rgba(255, 255, 255, 0.8);
  position: relative;
  overflow: hidden;
}

.category-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.category-card:hover::before {
  transform: scaleX(1);
}

.category-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
}

.category-icon {
  font-size: 64px;
  margin-bottom: 24px;
  display: block;
  transition: transform 0.3s ease;
}

.category-card:hover .category-icon {
  transform: scale(1.1);
}

.category-card h3 {
  font-size: 24px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 12px;
  letter-spacing: -0.01em;
}

.category-card p {
  color: #64748b;
  margin: 0;
  font-size: 16px;
  line-height: 1.5;
}

/* 特色商品区域 */
.featured-products {
  padding: 100px 0;
  background: white;
  position: relative;
}

.featured-products::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, #e2e8f0, transparent);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 60px;
  max-width: 1200px;
  margin-left: auto;
  margin-right: auto;
  padding: 0 20px;
}

.section-header h2 {
  font-size: 48px;
  font-weight: 800;
  color: #1a202c;
  margin: 0;
  letter-spacing: -0.02em;
}

.view-all {
  color: #667eea;
  text-decoration: none;
  font-weight: 600;
  font-size: 18px;
  transition: all 0.3s ease;
  padding: 12px 24px;
  border: 2px solid #667eea;
  border-radius: 50px;
  background: transparent;
}

.view-all:hover {
  background: #667eea;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 40px;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

.no-products {
  text-align: center;
  padding: 80px 0;
  color: #64748b;
  font-size: 18px;
}

/* 加载和错误状态 */
.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading {
  font-size: 18px;
  color: #6b7280;
}

.error {
  font-size: 18px;
  color: #ef4444;
  background: #fef2f2;
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #fecaca;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .hero-content h1 {
    font-size: 56px;
  }

  .categories h2,
  .section-header h2 {
    font-size: 42px;
  }

  .category-grid,
  .products-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
  }
}

@media (max-width: 1024px) {
  .hero {
    padding: 100px 0 80px;
    background-attachment: scroll;
  }

  .hero-content h1 {
    font-size: 48px;
  }

  .hero-content p {
    font-size: 20px;
  }

  .hero-buttons {
    flex-direction: column;
    gap: 15px;
  }

  .cta-button {
    width: 100%;
    max-width: 300px;
  }

  .stats {
    padding: 60px 0;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }

  .stat-number {
    font-size: 36px;
  }

  .categories,
  .featured-products {
    padding: 80px 0;
  }

  .categories h2,
  .section-header h2 {
    font-size: 36px;
  }

  .category-grid,
  .products-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 30px;
  }

  .category-card {
    padding: 40px 25px;
  }
}

@media (max-width: 768px) {
  .hero {
    padding: 80px 0 60px;
  }

  .hero-content h1 {
    font-size: 36px;
  }

  .hero-content p {
    font-size: 18px;
    margin-bottom: 40px;
  }

  .hero-buttons {
    flex-direction: column;
    gap: 15px;
  }

  .cta-button {
    padding: 15px 30px;
    font-size: 16px;
    width: 100%;
    max-width: 280px;
  }

  .stats {
    padding: 50px 0;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    padding: 0 20px;
  }

  .stat-item {
    padding: 25px 15px;
  }

  .stat-number {
    font-size: 32px;
  }

  .stat-label {
    font-size: 14px;
  }

  .categories,
  .featured-products {
    padding: 60px 0;
  }

  .categories h2,
  .section-header h2 {
    font-size: 32px;
  }

  .categories .subtitle {
    font-size: 18px;
    margin-bottom: 40px;
  }

  .category-grid,
  .products-grid {
    grid-template-columns: 1fr;
    gap: 20px;
    padding: 0 20px;
  }

  .category-card {
    padding: 35px 25px;
  }

  .category-icon {
    font-size: 56px;
  }

  .section-header {
    flex-direction: column;
    gap: 20px;
    text-align: center;
    padding: 0 20px;
  }

  .view-all {
    font-size: 16px;
    padding: 10px 20px;
  }
}

@media (max-width: 480px) {
  .hero-content {
    padding: 0 15px;
  }

  .hero-content h1 {
    font-size: 28px;
  }

  .hero-content p {
    font-size: 16px;
  }

  .categories h2,
  .section-header h2 {
    font-size: 28px;
  }

  .category-grid,
  .products-grid {
    padding: 0 15px;
  }

  .category-card {
    padding: 30px 20px;
  }

  .category-icon {
    font-size: 48px;
  }
}
