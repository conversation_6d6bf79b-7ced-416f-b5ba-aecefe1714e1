.search-page {
  padding: 40px 0;
  min-height: 100vh;
}

.search-header {
  text-align: center;
  margin-bottom: 40px;
  padding-bottom: 30px;
  border-bottom: 1px solid #e5e7eb;
}

.search-header h1 {
  font-size: 32px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 15px;
}

.search-header p {
  color: #6b7280;
  font-size: 16px;
}

.search-header strong {
  color: #2563eb;
  font-weight: 600;
}

.no-query {
  text-align: center;
  padding: 80px 0;
}

.no-query h1 {
  font-size: 32px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 20px;
}

.no-query p {
  color: #6b7280;
  font-size: 18px;
}

.search-content {
  max-width: 1200px;
  margin: 0 auto;
}

.sort-options {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.sort-options label {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.sort-options select {
  padding: 8px 12px;
  border: 2px solid #e5e7eb;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  cursor: pointer;
  min-width: 180px;
}

.sort-options select:focus {
  outline: none;
  border-color: #2563eb;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.no-results {
  text-align: center;
  padding: 80px 0;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.no-results h2 {
  font-size: 24px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 15px;
}

.no-results p {
  color: #6b7280;
  font-size: 16px;
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-top: 40px;
  padding: 30px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-btn {
  padding: 12px 24px;
  background: #2563eb;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.page-btn:hover:not(:disabled) {
  background: #1d4ed8;
  transform: translateY(-1px);
}

.page-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

.page-info {
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
}

/* 加载和错误状态 */
.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading {
  font-size: 18px;
  color: #6b7280;
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.error {
  font-size: 18px;
  color: #ef4444;
  background: #fef2f2;
  padding: 30px;
  border-radius: 12px;
  border: 1px solid #fecaca;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .search-page {
    padding: 20px 0;
  }
  
  .search-header h1 {
    font-size: 24px;
  }
  
  .search-header p {
    font-size: 14px;
  }
  
  .no-query h1 {
    font-size: 24px;
  }
  
  .no-query p {
    font-size: 16px;
  }
  
  .sort-options {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
    padding: 15px;
  }
  
  .sort-options select {
    width: 100%;
    min-width: auto;
  }
  
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
  }
  
  .no-results {
    padding: 60px 20px;
  }
  
  .no-results h2 {
    font-size: 20px;
  }
  
  .pagination {
    flex-direction: column;
    gap: 15px;
    padding: 20px;
  }
  
  .page-btn {
    width: 100%;
    max-width: 200px;
  }
}

@media (max-width: 480px) {
  .search-header {
    margin-bottom: 30px;
    padding-bottom: 20px;
  }
  
  .search-header h1 {
    font-size: 20px;
  }
  
  .products-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }
}
