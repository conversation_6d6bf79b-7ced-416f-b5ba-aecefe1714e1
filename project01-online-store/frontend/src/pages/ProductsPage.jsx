import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import productService from '../services/productService';
import ProductCard from '../components/product/ProductCard';
import './ProductsPage.css';

const ProductsPage = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState({});

  // 筛选状态
  const [filters, setFilters] = useState({
    page: parseInt(searchParams.get('page')) || 1,
    limit: 20,
    sort: searchParams.get('sort') || 'created_at',
    order: searchParams.get('order') || 'desc',
    category: searchParams.get('category') || '',
    brand: searchParams.get('brand') || '',
    min_price: searchParams.get('min_price') || '',
    max_price: searchParams.get('max_price') || ''
  });

  useEffect(() => {
    fetchProducts();
  }, [filters]);

  const fetchProducts = async () => {
    try {
      setLoading(true);
      const response = await productService.getProducts(filters);
      setProducts(response.data || []);
      setPagination(response.pagination || {});
    } catch (err) {
      setError('获取商品数据失败');
      console.error('获取商品失败:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleFilterChange = (key, value) => {
    const newFilters = { ...filters, [key]: value, page: 1 };
    setFilters(newFilters);
    
    // 更新URL参数
    const newSearchParams = new URLSearchParams();
    Object.entries(newFilters).forEach(([k, v]) => {
      if (v) newSearchParams.set(k, v);
    });
    setSearchParams(newSearchParams);
  };

  const handlePageChange = (page) => {
    const newFilters = { ...filters, page };
    setFilters(newFilters);
    
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set('page', page);
    setSearchParams(newSearchParams);
  };

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading">加载中...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="error-container">
        <div className="error">{error}</div>
      </div>
    );
  }

  return (
    <div className="products-page">
      <div className="container">
        <div className="page-header">
          <h1>商品列表</h1>
          <p>共找到 {pagination.total || 0} 件商品</p>
        </div>

        <div className="products-content">
          {/* 筛选侧边栏 */}
          <aside className="filters-sidebar">
            <div className="filter-section">
              <h3>排序</h3>
              <select
                value={`${filters.sort}-${filters.order}`}
                onChange={(e) => {
                  const [sort, order] = e.target.value.split('-');
                  handleFilterChange('sort', sort);
                  handleFilterChange('order', order);
                }}
              >
                <option value="created_at-desc">最新上架</option>
                <option value="price-asc">价格从低到高</option>
                <option value="price-desc">价格从高到低</option>
                <option value="sales-desc">销量最高</option>
                <option value="rating-desc">评分最高</option>
              </select>
            </div>

            <div className="filter-section">
              <h3>价格范围</h3>
              <div className="price-inputs">
                <input
                  type="number"
                  placeholder="最低价"
                  value={filters.min_price}
                  onChange={(e) => handleFilterChange('min_price', e.target.value)}
                />
                <span>-</span>
                <input
                  type="number"
                  placeholder="最高价"
                  value={filters.max_price}
                  onChange={(e) => handleFilterChange('max_price', e.target.value)}
                />
              </div>
            </div>

            <div className="filter-section">
              <h3>品牌</h3>
              <select
                value={filters.brand}
                onChange={(e) => handleFilterChange('brand', e.target.value)}
              >
                <option value="">全部品牌</option>
                <option value="Apple">Apple</option>
                <option value="小米">小米</option>
                <option value="华为">华为</option>
                <option value="Nike">Nike</option>
                <option value="Adidas">Adidas</option>
              </select>
            </div>
          </aside>

          {/* 商品网格 */}
          <main className="products-main">
            {products.length > 0 ? (
              <>
                <div className="products-grid">
                  {products.map((product) => (
                    <ProductCard key={product.id} product={product} />
                  ))}
                </div>

                {/* 分页 */}
                {pagination.pages > 1 && (
                  <div className="pagination">
                    <button
                      onClick={() => handlePageChange(pagination.page - 1)}
                      disabled={pagination.page <= 1}
                      className="page-btn"
                    >
                      上一页
                    </button>
                    
                    <span className="page-info">
                      第 {pagination.page} 页，共 {pagination.pages} 页
                    </span>
                    
                    <button
                      onClick={() => handlePageChange(pagination.page + 1)}
                      disabled={pagination.page >= pagination.pages}
                      className="page-btn"
                    >
                      下一页
                    </button>
                  </div>
                )}
              </>
            ) : (
              <div className="no-products">
                <p>没有找到符合条件的商品</p>
              </div>
            )}
          </main>
        </div>
      </div>
    </div>
  );
};

export default ProductsPage;
