.category-page {
  padding: 40px 0;
  min-height: 100vh;
}

.category-header {
  text-align: center;
  margin-bottom: 40px;
  padding-bottom: 30px;
  border-bottom: 1px solid #e5e7eb;
}

.category-header h1 {
  font-size: 32px;
  font-weight: 700;
  color: #111827;
  margin-bottom: 15px;
}

.category-description {
  color: #6b7280;
  font-size: 16px;
  margin-bottom: 10px;
  line-height: 1.6;
}

.product-count {
  color: #6b7280;
  font-size: 14px;
}

.category-content {
  display: grid;
  grid-template-columns: 250px 1fr;
  gap: 40px;
}

/* 筛选侧边栏 */
.filters-sidebar {
  background: white;
  padding: 30px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  height: fit-content;
  position: sticky;
  top: 100px;
}

.filter-section {
  margin-bottom: 30px;
}

.filter-section:last-child {
  margin-bottom: 0;
}

.filter-section h3 {
  font-size: 16px;
  font-weight: 600;
  color: #111827;
  margin-bottom: 15px;
}

.filter-section select {
  width: 100%;
  padding: 10px;
  border: 2px solid #e5e7eb;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  cursor: pointer;
}

.filter-section select:focus {
  outline: none;
  border-color: #2563eb;
}

.price-inputs {
  display: flex;
  align-items: center;
  gap: 10px;
}

.price-inputs input {
  flex: 1;
  padding: 10px;
  border: 2px solid #e5e7eb;
  border-radius: 6px;
  font-size: 14px;
}

.price-inputs input:focus {
  outline: none;
  border-color: #2563eb;
}

.price-inputs span {
  color: #6b7280;
  font-weight: 500;
}

/* 商品主区域 */
.products-main {
  min-height: 400px;
}

.products-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.no-products {
  text-align: center;
  padding: 80px 0;
  color: #6b7280;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.no-products p {
  font-size: 18px;
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-top: 40px;
  padding: 30px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-btn {
  padding: 12px 24px;
  background: #2563eb;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.page-btn:hover:not(:disabled) {
  background: #1d4ed8;
  transform: translateY(-1px);
}

.page-btn:disabled {
  background: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

.page-info {
  color: #6b7280;
  font-size: 14px;
  font-weight: 500;
}

/* 加载和错误状态 */
.loading-container,
.error-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading {
  font-size: 18px;
  color: #6b7280;
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.error {
  font-size: 18px;
  color: #ef4444;
  background: #fef2f2;
  padding: 30px;
  border-radius: 12px;
  border: 1px solid #fecaca;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .category-content {
    grid-template-columns: 1fr;
    gap: 30px;
  }
  
  .filters-sidebar {
    position: static;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    padding: 20px;
  }
  
  .filter-section {
    margin-bottom: 0;
  }
}

@media (max-width: 768px) {
  .category-page {
    padding: 20px 0;
  }
  
  .category-header h1 {
    font-size: 24px;
  }
  
  .category-description {
    font-size: 14px;
  }
  
  .products-grid {
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 20px;
  }
  
  .filters-sidebar {
    grid-template-columns: 1fr;
  }
  
  .pagination {
    flex-direction: column;
    gap: 15px;
    padding: 20px;
  }
  
  .page-btn {
    width: 100%;
    max-width: 200px;
  }
}
