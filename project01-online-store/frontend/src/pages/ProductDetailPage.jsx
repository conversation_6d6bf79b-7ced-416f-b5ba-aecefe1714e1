import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import productService from '../services/productService';
import ProductCard from '../components/product/ProductCard';
import './ProductDetailPage.css';

const ProductDetailPage = () => {
  const { id } = useParams();
  const [product, setProduct] = useState(null);
  const [recommendations, setRecommendations] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [selectedImage, setSelectedImage] = useState(0);

  useEffect(() => {
    if (id) {
      fetchProductDetail();
      fetchRecommendations();
    }
  }, [id]);

  const fetchProductDetail = async () => {
    try {
      setLoading(true);
      const response = await productService.getProductById(id);
      setProduct(response);
    } catch (err) {
      setError('获取商品详情失败');
      console.error('获取商品详情失败:', err);
    } finally {
      setLoading(false);
    }
  };

  const fetchRecommendations = async () => {
    try {
      const response = await productService.getRecommendations(id);
      setRecommendations(response || []);
    } catch (err) {
      console.error('获取推荐商品失败:', err);
    }
  };

  const formatPrice = (price) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY'
    }).format(price);
  };

  const getDiscountPercentage = () => {
    if (product?.original_price && product.original_price > product.price) {
      return Math.round(((product.original_price - product.price) / product.original_price) * 100);
    }
    return 0;
  };

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading">加载中...</div>
      </div>
    );
  }

  if (error || !product) {
    return (
      <div className="error-container">
        <div className="error">{error || '商品不存在'}</div>
      </div>
    );
  }

  const discountPercentage = getDiscountPercentage();

  return (
    <div className="product-detail-page">
      <div className="container">
        {/* 商品详情 */}
        <div className="product-detail">
          {/* 商品图片 */}
          <div className="product-images">
            <div className="main-image">
              <img 
                src={product.images?.[selectedImage] || '/placeholder-image.jpg'} 
                alt={product.name}
              />
              {discountPercentage > 0 && (
                <div className="discount-badge">
                  -{discountPercentage}%
                </div>
              )}
            </div>
            
            {product.images && product.images.length > 1 && (
              <div className="image-thumbnails">
                {product.images.map((image, index) => (
                  <img
                    key={index}
                    src={image}
                    alt={`${product.name} ${index + 1}`}
                    className={`thumbnail ${index === selectedImage ? 'active' : ''}`}
                    onClick={() => setSelectedImage(index)}
                  />
                ))}
              </div>
            )}
          </div>

          {/* 商品信息 */}
          <div className="product-info">
            <div className="breadcrumb">
              <span>{product.categories?.name}</span>
            </div>
            
            <h1 className="product-title">{product.name}</h1>
            
            {product.subtitle && (
              <p className="product-subtitle">{product.subtitle}</p>
            )}

            <div className="product-rating">
              <div className="stars">
                {[...Array(5)].map((_, index) => (
                  <span
                    key={index}
                    className={`star ${index < Math.floor(product.rating) ? 'filled' : ''}`}
                  >
                    ★
                  </span>
                ))}
              </div>
              <span className="rating-text">
                {product.rating} ({product.review_count}条评价)
              </span>
            </div>

            <div className="product-price">
              <span className="current-price">{formatPrice(product.price)}</span>
              {product.original_price && product.original_price > product.price && (
                <span className="original-price">{formatPrice(product.original_price)}</span>
              )}
            </div>

            <div className="product-meta">
              <div className="meta-item">
                <span className="label">品牌:</span>
                <span className="value">{product.brand}</span>
              </div>
              <div className="meta-item">
                <span className="label">库存:</span>
                <span className="value">{product.stock > 0 ? `${product.stock}件` : '缺货'}</span>
              </div>
              <div className="meta-item">
                <span className="label">销量:</span>
                <span className="value">{product.sales}件</span>
              </div>
            </div>

            <div className="product-description">
              <h3>商品描述</h3>
              <p>{product.description}</p>
            </div>

            {product.specifications && (
              <div className="product-specifications">
                <h3>规格参数</h3>
                <div className="specs-grid">
                  {Object.entries(product.specifications).map(([key, value]) => (
                    <div key={key} className="spec-item">
                      <span className="spec-label">{key}:</span>
                      <span className="spec-value">{value}</span>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 相关商品推荐 */}
        {recommendations.length > 0 && (
          <div className="recommendations">
            <h2>相关商品推荐</h2>
            <div className="products-grid">
              {recommendations.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ProductDetailPage;
