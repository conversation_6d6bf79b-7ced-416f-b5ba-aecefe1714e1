import React, { useState, useEffect } from 'react';
import { useSearchParams } from 'react-router-dom';
import productService from '../services/productService';
import ProductCard from '../components/product/ProductCard';
import './SearchPage.css';

const SearchPage = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [products, setProducts] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [pagination, setPagination] = useState({});

  const query = searchParams.get('q') || '';
  const page = parseInt(searchParams.get('page')) || 1;
  const sort = searchParams.get('sort') || 'created_at';
  const order = searchParams.get('order') || 'desc';

  useEffect(() => {
    if (query) {
      fetchSearchResults();
    } else {
      setProducts([]);
      setLoading(false);
    }
  }, [query, page, sort, order]);

  const fetchSearchResults = async () => {
    try {
      setLoading(true);
      const response = await productService.searchProducts({
        q: query,
        page,
        limit: 20,
        sort,
        order
      });
      setProducts(response.data || []);
      setPagination(response.pagination || {});
    } catch (err) {
      setError('搜索失败');
      console.error('搜索失败:', err);
    } finally {
      setLoading(false);
    }
  };

  const handleSortChange = (newSort, newOrder) => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set('sort', newSort);
    newSearchParams.set('order', newOrder);
    newSearchParams.set('page', '1');
    setSearchParams(newSearchParams);
  };

  const handlePageChange = (newPage) => {
    const newSearchParams = new URLSearchParams(searchParams);
    newSearchParams.set('page', newPage);
    setSearchParams(newSearchParams);
  };

  if (!query) {
    return (
      <div className="search-page">
        <div className="container">
          <div className="no-query">
            <h1>搜索商品</h1>
            <p>请在搜索框中输入关键词来搜索商品</p>
          </div>
        </div>
      </div>
    );
  }

  if (loading) {
    return (
      <div className="loading-container">
        <div className="loading">搜索中...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="error-container">
        <div className="error">{error}</div>
      </div>
    );
  }

  return (
    <div className="search-page">
      <div className="container">
        <div className="search-header">
          <h1>搜索结果</h1>
          <p>
            关键词: <strong>"{query}"</strong> 
            {pagination.total !== undefined && (
              <span> - 共找到 {pagination.total} 件商品</span>
            )}
          </p>
        </div>

        {products.length > 0 ? (
          <div className="search-content">
            {/* 排序选项 */}
            <div className="sort-options">
              <label>排序方式:</label>
              <select
                value={`${sort}-${order}`}
                onChange={(e) => {
                  const [newSort, newOrder] = e.target.value.split('-');
                  handleSortChange(newSort, newOrder);
                }}
              >
                <option value="created_at-desc">最新上架</option>
                <option value="price-asc">价格从低到高</option>
                <option value="price-desc">价格从高到低</option>
                <option value="sales-desc">销量最高</option>
                <option value="rating-desc">评分最高</option>
              </select>
            </div>

            {/* 商品网格 */}
            <div className="products-grid">
              {products.map((product) => (
                <ProductCard key={product.id} product={product} />
              ))}
            </div>

            {/* 分页 */}
            {pagination.pages > 1 && (
              <div className="pagination">
                <button
                  onClick={() => handlePageChange(pagination.page - 1)}
                  disabled={pagination.page <= 1}
                  className="page-btn"
                >
                  上一页
                </button>
                
                <span className="page-info">
                  第 {pagination.page} 页，共 {pagination.pages} 页
                </span>
                
                <button
                  onClick={() => handlePageChange(pagination.page + 1)}
                  disabled={pagination.page >= pagination.pages}
                  className="page-btn"
                >
                  下一页
                </button>
              </div>
            )}
          </div>
        ) : (
          <div className="no-results">
            <h2>没有找到相关商品</h2>
            <p>请尝试使用其他关键词搜索</p>
          </div>
        )}
      </div>
    </div>
  );
};

export default SearchPage;
