import api from './api';

// 分类相关API
export const categoryService = {
  // 获取分类列表
  getCategories: async () => {
    try {
      const response = await api.get('/categories');
      return response;
    } catch (error) {
      throw error;
    }
  },

  // 获取分类详情
  getCategoryById: async (id) => {
    try {
      const response = await api.get(`/categories/${id}`);
      return response;
    } catch (error) {
      throw error;
    }
  },
};

export default categoryService;
