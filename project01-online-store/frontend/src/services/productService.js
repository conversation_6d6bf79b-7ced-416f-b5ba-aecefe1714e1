import api from './api';

// 商品相关API
export const productService = {
  // 获取商品列表
  getProducts: async (params = {}) => {
    try {
      const response = await api.get('/products', { params });
      return response;
    } catch (error) {
      throw error;
    }
  },

  // 获取商品详情
  getProductById: async (id) => {
    try {
      const response = await api.get(`/products/${id}`);
      return response;
    } catch (error) {
      throw error;
    }
  },

  // 搜索商品
  searchProducts: async (params = {}) => {
    try {
      const response = await api.get('/products/search', { params });
      return response;
    } catch (error) {
      throw error;
    }
  },

  // 获取相关商品推荐
  getRecommendations: async (id) => {
    try {
      const response = await api.get(`/products/${id}/recommendations`);
      return response;
    } catch (error) {
      throw error;
    }
  },
};

export default productService;
