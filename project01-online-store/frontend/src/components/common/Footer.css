.footer {
  background-color: #1f2937;
  color: #d1d5db;
  margin-top: auto;
}

.footer-content {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 40px;
  padding: 40px 0;
}

.footer-section h3 {
  color: #2563eb;
  margin-bottom: 15px;
  font-size: 20px;
}

.footer-section h4 {
  color: #f9fafb;
  margin-bottom: 15px;
  font-size: 16px;
}

.footer-section p {
  margin-bottom: 10px;
  line-height: 1.6;
}

.footer-section ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-section ul li {
  margin-bottom: 8px;
}

.footer-section ul li a {
  color: #d1d5db;
  text-decoration: none;
  transition: color 0.2s;
}

.footer-section ul li a:hover {
  color: #2563eb;
}

.footer-bottom {
  border-top: 1px solid #374151;
  padding: 20px 0;
  text-align: center;
}

.footer-bottom p {
  margin: 0;
  color: #9ca3af;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .footer-content {
    grid-template-columns: 1fr;
    gap: 30px;
    padding: 30px 0;
  }
}
