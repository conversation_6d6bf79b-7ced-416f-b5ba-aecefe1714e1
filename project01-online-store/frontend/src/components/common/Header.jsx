import React, { useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import './Header.css';

const Header = () => {
  const [searchQuery, setSearchQuery] = useState('');
  const navigate = useNavigate();

  const handleSearch = (e) => {
    e.preventDefault();
    if (searchQuery.trim()) {
      navigate(`/search?q=${encodeURIComponent(searchQuery.trim())}`);
      setSearchQuery('');
    }
  };

  return (
    <header className="header">
      <div className="container">
        <div className="header-content">
          {/* Logo */}
          <Link to="/" className="logo">
            <h1>在线商城</h1>
          </Link>

          {/* 搜索框 */}
          <form className="search-form" onSubmit={handleSearch}>
            <input
              type="text"
              placeholder="搜索商品..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="search-input"
            />
            <button type="submit" className="search-button">
              搜索
            </button>
          </form>

          {/* 导航菜单 */}
          <nav className="nav">
            <Link to="/" className="nav-link">首页</Link>
            <Link to="/products" className="nav-link">商品</Link>
          </nav>
        </div>
      </div>
    </header>
  );
};

export default Header;
