import React from 'react';
import { Link } from 'react-router-dom';
import './ProductCard.css';

const ProductCard = ({ product }) => {
  const {
    id,
    name,
    subtitle,
    price,
    original_price,
    images,
    rating,
    review_count,
    categories
  } = product;

  // 获取第一张图片
  const imageUrl = images && images.length > 0 ? images[0] : '/placeholder-image.jpg';

  // 格式化价格
  const formatPrice = (price) => {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY'
    }).format(price);
  };

  // 计算折扣百分比
  const getDiscountPercentage = () => {
    if (original_price && original_price > price) {
      return Math.round(((original_price - price) / original_price) * 100);
    }
    return 0;
  };

  const discountPercentage = getDiscountPercentage();

  return (
    <div className="product-card">
      <Link to={`/products/${id}`} className="product-link">
        <div className="product-image">
          <img src={imageUrl} alt={name} />
          {discountPercentage > 0 && (
            <div className="discount-badge">
              -{discountPercentage}%
            </div>
          )}
        </div>
        
        <div className="product-info">
          <div className="product-category">
            {categories?.name}
          </div>
          
          <h3 className="product-name">{name}</h3>
          
          {subtitle && (
            <p className="product-subtitle">{subtitle}</p>
          )}
          
          <div className="product-rating">
            <div className="stars">
              {[...Array(5)].map((_, index) => (
                <span
                  key={index}
                  className={`star ${index < Math.floor(rating) ? 'filled' : ''}`}
                >
                  ★
                </span>
              ))}
            </div>
            <span className="rating-text">
              {rating} ({review_count}条评价)
            </span>
          </div>
          
          <div className="product-price">
            <span className="current-price">{formatPrice(price)}</span>
            {original_price && original_price > price && (
              <span className="original-price">{formatPrice(original_price)}</span>
            )}
          </div>
        </div>
      </Link>
    </div>
  );
};

export default ProductCard;
