# 任务管理系统 - 系统架构设计文档

## 1. 架构概述

### 1.1 架构模式
采用前后端分离的三层架构模式：
- **表现层**: React前端应用
- **业务逻辑层**: Node.js + Express API服务
- **数据访问层**: Supabase数据库服务

### 1.2 技术选型

#### 1.2.1 前端技术栈
- **框架**: React 18.2+
- **构建工具**: Vite 4.0+
- **状态管理**: React Hooks + Context API
- **HTTP客户端**: Axios
- **UI组件**: 自定义组件 + CSS3
- **路由**: React Router v6

**选型理由**:
- React生态成熟，开发效率高
- Vite构建速度快，开发体验好
- 轻量级状态管理适合中小型项目

#### 1.2.2 后端技术栈
- **运行时**: Node.js 18+
- **Web框架**: Express.js 4.18+
- **数据库**: Supabase (PostgreSQL)
- **认证**: Supabase Auth
- **API文档**: Swagger/OpenAPI 3.0

**选型理由**:
- Node.js与前端技术栈统一，降低学习成本
- Express.js轻量级，适合快速开发
- Supabase提供完整的BaaS解决方案

#### 1.2.3 数据库技术
- **数据库**: PostgreSQL (通过Supabase)
- **ORM**: Supabase JavaScript客户端
- **实时功能**: Supabase Realtime

**选型理由**:
- PostgreSQL功能强大，支持复杂查询
- Supabase提供实时数据同步
- 内置认证和授权功能

## 2. 系统架构图

```
┌─────────────────┐    HTTP/HTTPS    ┌─────────────────┐
│   React前端     │ ◄──────────────► │  Express API    │
│   (Vite构建)    │                  │   服务器        │
└─────────────────┘                  └─────────────────┘
                                              │
                                              │ API调用
                                              ▼
                                    ┌─────────────────┐
                                    │   Supabase      │
                                    │   数据库服务    │
                                    └─────────────────┘
```

## 3. 模块设计

### 3.1 前端模块结构

```
src/
├── components/          # 可复用组件
│   ├── common/         # 通用组件
│   ├── task/           # 任务相关组件
│   └── auth/           # 认证相关组件
├── pages/              # 页面组件
│   ├── Login.jsx       # 登录页面
│   ├── Register.jsx    # 注册页面
│   ├── Dashboard.jsx   # 主页面
│   └── TaskDetail.jsx  # 任务详情页面
├── hooks/              # 自定义Hooks
├── services/           # API服务
├── utils/              # 工具函数
├── contexts/           # React Context
└── styles/             # 样式文件
```

### 3.2 后端模块结构

```
server/
├── routes/             # 路由定义
│   ├── auth.js         # 认证路由
│   ├── tasks.js        # 任务路由
│   └── categories.js   # 分类路由
├── middleware/         # 中间件
│   ├── auth.js         # 认证中间件
│   └── validation.js   # 数据验证中间件
├── services/           # 业务逻辑服务
├── utils/              # 工具函数
└── config/             # 配置文件
```

## 4. 数据库设计

### 4.1 数据表结构

#### 4.1.1 用户表 (users)
```sql
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    username VARCHAR(100) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

#### 4.1.2 分类表 (categories)
```sql
CREATE TABLE categories (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(50) NOT NULL,
    color VARCHAR(7) DEFAULT '#3B82F6',
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(name, user_id)
);
```

#### 4.1.3 任务表 (tasks)
```sql
CREATE TABLE tasks (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    title VARCHAR(100) NOT NULL,
    description TEXT,
    status VARCHAR(20) DEFAULT 'todo' CHECK (status IN ('todo', 'in_progress', 'completed')),
    priority VARCHAR(10) DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high')),
    due_date TIMESTAMP WITH TIME ZONE,
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    category_id UUID REFERENCES categories(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 4.2 数据关系
- 用户与任务：一对多关系
- 用户与分类：一对多关系
- 分类与任务：一对多关系

## 5. API设计

### 5.1 认证API
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `GET /api/auth/profile` - 获取用户信息

### 5.2 任务API
- `GET /api/tasks` - 获取任务列表
- `POST /api/tasks` - 创建任务
- `GET /api/tasks/:id` - 获取任务详情
- `PUT /api/tasks/:id` - 更新任务
- `DELETE /api/tasks/:id` - 删除任务

### 5.3 分类API
- `GET /api/categories` - 获取分类列表
- `POST /api/categories` - 创建分类
- `PUT /api/categories/:id` - 更新分类
- `DELETE /api/categories/:id` - 删除分类

## 6. 安全设计

### 6.1 认证机制
- 使用Supabase Auth进行用户认证
- JWT Token管理用户会话
- 自动刷新Token机制

### 6.2 授权控制
- 基于用户ID的数据隔离
- API级别的权限验证
- 前端路由保护

### 6.3 数据安全
- 密码加密存储
- SQL注入防护
- XSS攻击防护
- CORS配置

## 7. 性能优化

### 7.1 前端优化
- 组件懒加载
- 图片优化
- 代码分割
- 缓存策略

### 7.2 后端优化
- 数据库索引优化
- API响应缓存
- 连接池管理
- 查询优化

### 7.3 数据库优化
- 合理的索引设计
- 查询性能监控
- 数据分页处理

## 8. 部署架构

### 8.1 开发环境
- 前端：本地Vite开发服务器 (端口3000)
- 后端：本地Express服务器 (端口5000)
- 数据库：Supabase云服务

### 8.2 生产环境建议
- 前端：静态文件部署到CDN
- 后端：Node.js服务器部署
- 数据库：Supabase生产环境
- 负载均衡和监控

## 9. 监控和日志

### 9.1 日志记录
- API访问日志
- 错误日志
- 性能日志

### 9.2 监控指标
- 响应时间监控
- 错误率监控
- 用户活跃度监控

## 10. 扩展性考虑

### 10.1 水平扩展
- 微服务架构准备
- 数据库分片策略
- 缓存层设计

### 10.2 功能扩展
- 插件系统设计
- API版本管理
- 第三方集成接口
